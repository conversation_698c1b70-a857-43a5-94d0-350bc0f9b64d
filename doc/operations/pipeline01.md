# Pipeline 01: Сканер-инвентаризатор источников книг

```bash
# Очистить Redis
redis-cli FLUSHDB

# Посмотреть записи в Redis (новые имена очередей)
redis-cli LRANGE books:queue:20_parsing_new 0 -1
redis-cli LRANGE books:queue:20_parsing_processing 0 -1
redis-cli LRANGE books:queue:completed 0 -1

# Проверить активные задачи
redis-cli SMEMBERS books:set:queued_ids
```

## 📖 Назначение

Pipeline_01 `scanner_inventorizer.py` — новый высокопроизводительный инвентаризатор с продвинутой пакетной дедупликацией. Его единственная задача — просканировать все источники, сравнить их содержимое с уже обработанными книгами в PostgreSQL и создать очередь задач только для новых, уникальных книг.

**АРХИТЕКТУРНЫЕ ПРИНЦИПЫ:**
1. **PostgreSQL (book_sources)** — единственный источник правды об обработанных книгах
2. **Redis SET_QUEUED_IDS** — отслеживание активных задач в очередях
3. **Пакетная обработка** всех проверок для минимизации накладных расходов
4. **Абстракция от типа хранилища** (локальная ФС / S3)
5. **Идемпотентность и отказоустойчивость**

Основные задачи:
1. Сканировать архивы в исходных директориях через StorageManager
2. Извлекать метаданные архивов и списки книг внутри них
3. **ПАКЕТНАЯ ДЕДУПЛИКАЦИЯ:** Redis SET_QUEUED_IDS → PostgreSQL book_sources
4. Создавать задачи с полными метаданными для воркера-потребителя

> **📋 Документация очередей**: См. [../redis_queues.md](../redis_queues.md) для полного описания архитектуры очередей Redis.

## 🏗️ Архитектура

```
SOURCE_DIRS                    Redis (очереди + SET)                ВОРКЕР
┌─────────────────┐       ┌─────────────────────────────────┐    ┌─────────────────┐
│ zip_flibusta/   │       │ books:queue:20_parsing_new      │    │ Прямая работа   │
│ zip_searchfloor/│  -->  │ [{"source_type":1,"source_id":  │    │ с архивами:     │
│ zip_anna/       │       │   826222,"archive_path":"...",  │--> │ archive_path +  │
└─────────────────┘       │   "book_filename":"826222.fb2", │    │ book_filename   │
     ^                    │   "archive_mtime":1640995200}]  │    │ из задачи       │
     │                    │ (новые, ~150 байт)              │    └─────────────────┘
     │ StorageManager      │                                 │             │
     │ абстракция          │ books:queue:20_parsing_processing│         обработка
     └─────────────────────│ [task3] (в работе)              │             │
                          │                                 │             v
                          │ books:set:queued_ids            │      ┌─────────────────┐
                          │ {"1:826222", "2:123456"}        │      │ PostgreSQL      │
                          │ (быстрая проверка O(1))         │      │ book_sources    │
                          └─────────────────────────────────┘      │ единственный    │
                                                                   │ источник правды │
                                                                   └─────────────────┘
```

**Ключевые изменения архитектуры:**
- **Полные метаданные в задачах** — воркер получает всю информацию для обработки
- **Отказ от кэша SET_PROCESSED** — PostgreSQL book_sources единственный источник правды
- **Пакетная дедупликация** — проверки выполняются пакетами для оптимизации
- **StorageManager абстракция** — поддержка локальной ФС и S3

## 📂 Структура задачи (НОВЫЙ ФОРМАТ)

```json
{
  "source_type": 1,
  "source_id": 826222,
  "archive_path": "/path/to/source/archive_826222.zip",
  "book_filename": "826222.fb2",
  "archive_mtime": 1640995200.123
}
```

**Поля задачи:**
- `source_type` — тип источника (1=flibusta, 2=searchfloor, 3=anna)
- `source_id` — ID файла (только цифры, извлеченные регексом)
- `archive_path` — полный путь к архиву
- `book_filename` — имя файла книги внутри архива
- `archive_mtime` — время модификации архива (для отслеживания изменений)

**Преимущества нового формата:**
- **Полная самодостаточность** — воркер получает всю информацию для обработки
- **Отказ от кэша путей** — не нужно восстанавливать пути через сканирование
- **Поддержка архивов** — прямая работа с ZIP/RAR архивами
- **Отслеживание изменений** — archive_mtime для детекции обновлений

## 🔍 Пакетная дедупликация

**Новый алгоритм пакетной дедупликации с карантином:**

1. **Сбор кандидатов**: сканирование всех архивов через StorageManager
2. **Пакетная проверка Redis SET**: проверка активных задач пакетами по 5000 элементов
3. **Пакетная проверка карантина**: проверка таблицы `quarantined_books` пакетами по 1000 элементов *(новый шаг)*
4. **Пакетная проверка PostgreSQL**: проверка обработанных книг в `book_sources` одним оптимизированным запросом для всех пар *(оптимизировано)*
5. **Атомарная постановка в очередь**: Redis pipeline для добавления задач

**Преимущества пакетного подхода:**
- **Минимизация накладных расходов**: меньше сетевых запросов к Redis/PostgreSQL
- **Масштабируемость**: эффективная обработка больших объемов данных
- **Отказоустойчивость**: атомарные операции через Redis pipeline
- **Производительность**: оптимизация для больших дампов (10,000+ файлов)

## 🎯 Сценарии использования

### 📅 Ежедневное сканирование (10-20 новых книг)

**Команда:**
```bash
python -m app.ingestion.scanner_inventorizer
```

**Характеристики:**
- Запуск: каждые 1-3 часа (cron)
- Файлов: 10-20 новых за день
- Время: ~50ms пакетных проверок
- RAM: минимальное потребление (~50 MB)
- Алгоритм: пакетная дедупликация

**Производительность:**
```
20 файлов × (пакетная проверка Redis SET + PostgreSQL) = ~50ms
Пакетный размер: 5000 для Redis, 1000 для PostgreSQL
Порядок проверок: Redis SET_QUEUED_IDS → PostgreSQL book_sources
```

### 📦 Большие дампы (2000+ книг)

**Команда:**
```bash
# Загружаем дамп в директории
cp monthly_dump_*.zip /path/to/zip_flibusta/

# Запускаем сканирование (автоматическая пакетная обработка)
python -m app.ingestion.scanner_inventorizer
```

**Характеристики:**
- Запуск: ручной или автоматический
- Файлов: 2000+ новых книг
- Время: пропорционально количеству файлов
- RAM: ~100-200 MB для пакетной обработки
- Оптимизация: автоматические пакеты по 5000/1000 элементов

**Производительность:**
```
Пакетная обработка: эффективная для любого объема
Автоматическое логирование прогресса для больших объемов (>10,000)
Отказ от кэширования в пользу прямых пакетных запросов
PostgreSQL book_sources - единственный источник правды
```

## 🛠️ Команды и опции

### Основные команды

```bash
# Основное сканирование (единственный режим)
python -m app.ingestion.scanner_inventorizer

# Альтернативный запуск через скрипт
python run_01_scanner_inventorizer.py

# Тихий режим для cron
python -m app.ingestion.scanner_inventorizer --quiet

# Справка по опциям
python -m app.ingestion.scanner_inventorizer --help
```

### Описание опций

| Опция | Назначение | Когда использовать |
|-------|------------|-------------------|
| `--quiet` | Тихий режим - только критические ошибки в вывод | Запуск через cron |
| `--batch-size-redis` | Размер пакета для Redis проверок (по умолчанию 5000) | Тонкая настройка производительности |
| `--batch-size-db` | Размер пакета для PostgreSQL проверок (по умолчанию 1000) | Тонкая настройка производительности |

**Упрощение архитектуры:**
- **Убраны опции кэширования** — PostgreSQL единственный источник правды
- **Автоматическая пакетная обработка** — оптимизация встроена в алгоритм
- **Единый режим работы** — нет необходимости в разных стратегиях

## ⚡ Оптимизация производительности

### ✅ Автоматические оптимизации:

- **Пакетная дедупликация**: проверки выполняются пакетами для минимизации накладных расходов
- **Настраиваемые размеры пакетов**: Redis (5000) и PostgreSQL (1000) оптимизированы для разных нагрузок
- **Прогрессивное логирование**: автоматические отчеты о прогрессе для больших объемов
- **Единственный источник правды**: PostgreSQL book_sources исключает проблемы синхронизации

### 🎯 Рекомендации по настройке:

- **Малые объемы** (< 1000 файлов): используйте настройки по умолчанию
- **Большие дампы** (> 10000 файлов): рассмотрите увеличение размеров пакетов
- **Медленный PostgreSQL**: уменьшите SCANNER_DB_CHECK_BATCH_SIZE до 500
- **Быстрый Redis**: увеличьте SCANNER_REDIS_CHECK_BATCH_SIZE до 10000

## 📊 Мониторинг и логи

### Структура логов

```
📂 Начинаем инвентаризацию источников...
📂 Источник zip_flibusta: 1250 архивов
📂 Источник zip_searchfloor: 890 архивов
📂 Источник zip_anna: 2100 архивов
📚 Найдено кандидатов: 4240
🔍 Начинаем пакетную дедупликацию для 4240 кандидатов
📊 Обработано 10000/15000 кандидатов
✅ Дедупликация завершена. Новых книг: 125
📤 Добавляем 125 новых задач в очередь
✅ Успешно добавлено 125 задач в очередь
⏱️ Сканирование завершено за 2.34 секунд
🏁 Сканирование завершено. Архивов: 4240, Книг найдено: 4240, Уже в очередях: 15, В карантине: 8, Уже обработано: 4100, Новых задач: 125, Ошибок: 0
```

### Ключевые метрики

- **Архивов**: Общее количество найденных архивов
- **Книг найдено**: Общее количество книг-кандидатов
- **Уже в очередях**: Дубликаты из Redis SET_QUEUED_IDS (активные задачи)
- **В карантине**: Дубликаты из PostgreSQL quarantined_books *(новая метрика)*
- **Уже обработано**: Дубликаты из PostgreSQL book_sources (успешно обработанные)
- **Новых задач**: Добавлено в очередь для обработки
- **Ошибок**: Количество ошибок при обработке

### Автоматические рекомендации

```
📊 Обработано 10000/15000 кандидатов (прогресс для больших объемов)
⏱️ Сканирование завершено за 2.34 секунд (производительность)
✅ Дедупликация завершена. Новых книг: 125 (эффективность фильтрации)
```

## 🔧 Настройка и конфигурация

### Переменные окружения (.env)

```bash
# Источники данных (через запятую)
SOURCE_DIRS=/mnt/d/Project/books/zip/zip_flibusta,/mnt/d/Project/books/zip/zip_searchfloor,/mnt/d/Project/books/zip/zip_anna

# Redis для очередей
REDIS_URL=redis://localhost:6379/0
WORKER_TIMEOUT=300

# PostgreSQL для проверки дубликатов
POSTGRES_HOST=localhost
POSTGRES_PORT=65432
POSTGRES_DB=books
POSTGRES_USER=books
POSTGRES_PASSWORD=books_password

# Настройки пакетной обработки
SCANNER_REDIS_CHECK_BATCH_SIZE=5000  # Размер пакета для Redis проверок
SCANNER_DB_CHECK_BATCH_SIZE=1000     # Размер пакета для PostgreSQL проверок
```

### Типы источников

```python
SOURCE_TYPE_MAP = {
    'zip_flibusta': 1,     # Flibusta.is
    'zip_searchfloor': 2,  # Поисковая система
    'zip_anna': 3          # Anna's Archive
}
```

### Извлечение ID из имен файлов

```python
# Функция extract_source_id() в app/utils.py
# Извлекает числовой ID из имени файла

# Примеры:
# book_12345.fb2 → 12345
# archive-new-2024.epub → 2024
# flibusta.123456.txt → 123456
# prefix.suffix.789012.pdf → 789012
```

## 🚀 Автоматизация (Cron)

### Ежедневное сканирование

```bash
# Каждые 3 часа в рабочее время (тихий режим)
0 */3 * * * cd /path/to/books && python -m app.ingestion.scanner_inventorizer --quiet >> /var/log/books/scanner.log 2>&1

# Или раз в час
0 * * * * cd /path/to/books && python -m app.ingestion.scanner_inventorizer --quiet >> /var/log/books/scanner.log 2>&1

# Альтернативный запуск через скрипт
0 */3 * * * cd /path/to/books && python run_01_scanner_inventorizer.py --quiet >> /var/log/books/scanner.log 2>&1
```

### Мониторинг Redis очередей

```bash
# Ежедневная проверка размеров очередей в 06:00
0 6 * * * redis-cli LLEN books:queue:20_parsing_new >> /var/log/books/queue_monitoring.log 2>&1
```

## 🐛 Troubleshooting

### Проблема: Файлы не добавляются в очередь

**Симптомы:**
- `Новых задач: 0` при наличии новых архивов
- Архивы присутствуют в директориях

**Причины и решения:**

1. **Нет книг с цифровым ID в архивах**
   ```
   Проверьте лог: "⏭️ Пропускаем файл без ID: filename"
   Книги внутри архивов должны содержать цифры в имени
   ```

2. **Файлы уже обработаны**
   ```bash
   # Проверьте в PostgreSQL
   psql $DATABASE_URL -c "SELECT source_type, source_id FROM book_sources ORDER BY source_type, source_id LIMIT 10;"
   ```

3. **Файлы уже в очереди**
   ```bash
   # Проверьте Redis SET
   redis-cli SCARD books:set:queued_ids
   redis-cli SMEMBERS books:set:queued_ids | head -10
   ```

4. **Неправильная структура директорий**
   ```bash
   # Проверьте SOURCE_DIRS в .env
   echo $SOURCE_DIRS

   # Убедитесь что директории существуют
   ls -la /path/to/sources/
   ```

### Проблема: Redis недоступен

**Симптомы:**
- `❌ Критическая ошибка при сканировании`
- Подключение отклонено

**Решения:**
```bash
# Проверьте Redis
redis-cli ping

# Проверьте URL подключения
echo $REDIS_URL

# Перезапустите Redis
sudo systemctl restart redis
# или
docker restart redis-container
```

### Проблема: PostgreSQL медленно отвечает

**Симптомы:**
- Сканирование долго выполняется
- Таймауты подключений

**Решения:**
```sql
-- Проверьте индекс
\d book_sources

-- Должен быть: uq_source_identity ON book_sources (source_type, source_id)

-- Анализ производительности
EXPLAIN ANALYZE SELECT source_id FROM book_sources WHERE source_type = 1 AND source_id = ANY(ARRAY[12345, 67890]);

-- Обновите статистику
ANALYZE book_sources;
```

### Проблема: Медленная пакетная обработка

**Симптомы:**
- Долгое выполнение для больших объемов
- Высокая нагрузка на БД

**Решения:**
```bash
# Уменьшите размеры пакетов
export SCANNER_DB_CHECK_BATCH_SIZE=500
export SCANNER_REDIS_CHECK_BATCH_SIZE=2000

# Проверьте использование памяти
redis-cli INFO memory

# Мониторинг PostgreSQL
psql $DATABASE_URL -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
```

## 📈 Мониторинг производительности

### Ключевые метрики для мониторинга

```bash
# Размеры очередей (новые имена)
redis-cli LLEN books:queue:20_parsing_new
redis-cli LLEN books:queue:20_parsing_processing
redis-cli LLEN books:queue:completed

# Размер SET активных задач
redis-cli SCARD books:set:queued_ids

# Количество обработанных файлов в БД
psql $DATABASE_URL -c "SELECT COUNT(*) FROM book_sources;"

# Распределение по источникам
psql $DATABASE_URL -c "SELECT source_type, COUNT(*) FROM book_sources GROUP BY source_type;"

# Производительность пакетных операций
psql $DATABASE_URL -c "EXPLAIN ANALYZE SELECT source_id FROM book_sources WHERE source_type = 1 AND source_id = ANY(ARRAY[1,2,3]);"
```

### Графики для мониторинга

1. **Размер очереди задач** — должен быстро обнуляться после запуска воркера
2. **Время выполнения сканера** — норма: 1-60 секунд в зависимости от объема
3. **Количество новых задач** — показывает активность источников
4. **Эффективность дедупликации** — соотношение найденных/новых задач
5. **Использование RAM** — контроль пакетной обработки

## 🎯 Рекомендации по использованию

### Оптимальная стратегия

1. **Ежедневно**: `python -m app.ingestion.scanner_inventorizer` (единственный режим)
2. **Большие дампы**: тот же команда (автоматическая оптимизация)
3. **Мониторинг**: проверка логов и размеров очередей
4. **При проблемах**: анализ логов и настройка размеров пакетов

### Производительность

- **Пакетная обработка**: оптимальна для любого объема (10-100,000+ файлов)
- **PostgreSQL индекс**: обеспечивает проверку дубликатов за 1-2ms на пакет
- **Redis SET**: O(1) проверка активных задач
- **Автоматическое масштабирование**: эффективность не зависит от объема

### Потребление ресурсов

- **RAM**: ~50-200 MB (зависит от размеров пакетов)
- **CPU**: минимальное (I/O bound операции)
- **Диск**: только чтение архивов (файлы не перемещаются)
- **Сеть**: оптимизированные пакетные запросы к Redis/PostgreSQL

## 📁 Новая архитектура обработки файлов

### Отказ от перемещения файлов

В новой архитектуре **исходные файлы остаются на месте**. Результаты обработки сохраняются в отдельные директории:

```
SOURCE_DIRS/                    # Исходные архивы (не перемещаются)
├── zip_flibusta/
│   ├── archive_826222.zip     # Остается на месте
│   └── archive_828931.zip
├── zip_searchfloor/
│   └── books_123456.zip
└── zip_anna/
    └── collection_789012.zip

STORAGE/                        # Результаты обработки (CANONICAL_STORAGE_PATH)
├── canonical_json/             # Канонические JSON книг (многоуровневая структура по UUID)
│   ├── 01/8f/7b/018f7b8c-a0f8-7177-8c6a-3a1b5b9d4a1e.json.zst
│   └── ...
└── chunks/                     # RAG чанки (будущее)
```

### Преимущества новой архитектуры

1. **Сохранность исходников** — архивы остаются в первоначальном виде
2. **Упрощение логики** — нет необходимости в перемещении файлов
3. **Отказоустойчивость** — можно переобработать любой файл
4. **Параллельная обработка** — несколько воркеров могут работать с одними источниками
5. **Простота бэкапов** — исходники и результаты разделены

### Отслеживание состояния

Состояние обработки отслеживается через:
- **PostgreSQL book_sources** — единственный источник правды об обработанных книгах
- **PostgreSQL quarantined_books** — логический карантин проблемных файлов
- **Redis SET_QUEUED_IDS** — активные задачи в очередях
- **Файловая система** — результаты в canonical_json/ (многоуровневая структура по UUID)

---

**Важно**: Сканер-инвентаризатор НЕ перемещает и НЕ удаляет исходные файлы. Он только создает задачи для воркера-потребителя (`run_20_process_book_worker.py`), который сохраняет результаты в отдельные директории.
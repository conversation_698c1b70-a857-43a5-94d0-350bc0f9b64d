# app/ingestion/scanner_inventorizer.py

import json
import logging
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Iterator, Optional

import redis

from app import settings
from app.database.connection import get_db_connection
from app.database.queries import get_quarantined_ids
from app.storage import LocalStorageManager, StorageManager
from app.utils import extract_source_id

logger = logging.getLogger(__name__)


@dataclass
class BookCandidate:
    """Кандидат на обработку - книга найденная в архиве."""

    source_type: int
    source_id: int
    archive_path: str
    book_filename: str
    archive_mtime: float

    def get_key(self) -> str:
        """Возвращает ключ для проверок в Redis SET."""
        return f"{self.source_type}:{self.source_id}"


@dataclass
class ScannerStats:
    """Статистика работы сканера."""

    archives_found: int = 0
    books_found: int = 0
    already_in_queue: int = 0
    already_in_quarantine: int = 0
    already_processed: int = 0
    new_tasks_created: int = 0
    errors: int = 0

    def log_summary(self):
        """Выводит итоговую статистику."""
        logger.info(
            f"🏁 Сканирование завершено. "
            f"Архивов: {self.archives_found}, "
            f"Книг найдено: {self.books_found}, "
            f"Уже в очередях: {self.already_in_queue}, "
            f"В карантине: {self.already_in_quarantine}, "
            f"Уже обработано: {self.already_processed}, "
            f"Новых задач: {self.new_tasks_created}, "
            f"Ошибок: {self.errors}"
        )


class ScannerInventorizer:
    """Новый сканер-инвентаризатор с продвинутой пакетной дедупликацией.

    АРХИТЕКТУРНЫЕ ПРИНЦИПЫ:
    1. PostgreSQL (book_sources) - единственный источник правды об обработанных книгах
    2. Redis SET_QUEUED_IDS - отслеживание активных задач в очередях
    3. Пакетная обработка всех проверок для минимизации накладных расходов
    4. Абстракция от типа хранилища (локальная ФС / S3)
    5. Идемпотентность и отказоустойчивость
    """

    def __init__(self, storage_manager: Optional[StorageManager] = None):
        """Инициализация сканера.

        Args:
            storage_manager: Менеджер хранилища. Если None, создается LocalStorageManager
        """
        self.storage_manager = storage_manager or LocalStorageManager()
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.stats = ScannerStats()

    def scan_all_sources(self) -> ScannerStats:
        """Главная функция сканера - обрабатывает все источники из SOURCE_DIRS.

        Returns:
            ScannerStats: Статистика сканирования
        """
        logger.info("📂 Начинаем инвентаризацию источников...")
        start_time = time.time()

        try:
            # Шаг 1: Собираем всех кандидатов
            all_candidates = list(self._collect_all_candidates())
            self.stats.books_found = len(all_candidates)

            if not all_candidates:
                logger.info("📭 Новых книг для обработки не найдено")
                return self.stats

            logger.info(f"📚 Найдено кандидатов: {len(all_candidates)}")

            # Шаг 2: Пакетная дедупликация
            new_books = self._batch_deduplication(all_candidates)

            # Шаг 3: Постановка в очередь
            if new_books:
                self._atomic_queue_tasks(new_books)

            # Итоговая статистика
            elapsed = time.time() - start_time
            logger.info(f"⏱️ Сканирование завершено за {elapsed:.2f} секунд")
            self.stats.log_summary()

            return self.stats

        except Exception as e:
            self.stats.errors += 1
            logger.error(f"❌ Критическая ошибка при сканировании: {e}", exc_info=True)
            raise

    def _collect_all_candidates(self) -> Iterator[BookCandidate]:
        """Собирает всех кандидатов из всех источников.

        Yields:
            BookCandidate: Кандидат на обработку
        """
        for source_dir in settings.SOURCE_DIRS:
            source_path = str(source_dir)
            source_name = source_dir.name
            source_type = settings.SOURCE_TYPE_MAP.get(source_name)

            if source_type is None:
                logger.warning(f"⚠️ Неизвестный тип источника: {source_name}, пропускаем")
                continue

            try:
                # Получаем все архивы из источника
                archive_paths = list(self.storage_manager.list_archives(source_path))
                self.stats.archives_found += len(archive_paths)

                if not archive_paths:
                    logger.debug(f"📂 Источник {source_name} пуст")
                    continue

                logger.info(f"📂 Источник {source_name}: {len(archive_paths)} архивов")

                # Обрабатываем каждый архив
                for archive_path in archive_paths:
                    try:
                        yield from self._process_archive(archive_path, source_type)
                    except Exception as e:
                        self.stats.errors += 1
                        logger.error(f"❌ Ошибка обработки архива {archive_path}: {e}")
                        continue

            except Exception as e:
                self.stats.errors += 1
                logger.error(f"❌ Ошибка сканирования источника {source_name}: {e}")
                continue

    def _process_archive(self, archive_path: str, source_type: int) -> Iterator[BookCandidate]:
        """Обрабатывает один архив и извлекает кандидатов.

        Args:
            archive_path: Путь к архиву
            source_type: Тип источника

        Yields:
            BookCandidate: Кандидат на обработку
        """
        try:
            # Получаем метаданные архива
            metadata = self.storage_manager.get_archive_metadata(archive_path)

            # Получаем список книг в архиве
            book_files = self.storage_manager.list_books_in_archive(archive_path)

            if not book_files:
                logger.debug(f"📂 Архив {Path(archive_path).name} не содержит книжных файлов")
                return

            # Создаем кандидатов для каждой книги
            for book_filename in book_files:
                source_id = extract_source_id(Path(book_filename))

                if source_id is None:
                    logger.debug(f"⏭️ Пропускаем файл без ID: {book_filename}")
                    continue

                yield BookCandidate(
                    source_type=source_type,
                    source_id=source_id,
                    archive_path=archive_path,
                    book_filename=book_filename,
                    archive_mtime=metadata.archive_mtime,
                )

        except Exception as e:
            logger.error(f"❌ Ошибка обработки архива {archive_path}: {e}")
            raise

    def _batch_deduplication(self, candidates: list[BookCandidate]) -> list[BookCandidate]:
        """Выполняет пакетную дедупликацию кандидатов.

        АЛГОРИТМ:
        1. Разбиваем на пакеты размером SCANNER_REDIS_CHECK_BATCH_SIZE
        2. Для каждого пакета: проверка Redis SET → PostgreSQL batch
        3. Возвращаем только новые книги

        Args:
            candidates: Список кандидатов

        Returns:
            list[BookCandidate]: Список новых книг для обработки
        """
        if not candidates:
            return []

        logger.info(f"🔍 Начинаем пакетную дедупликацию для {len(candidates)} кандидатов")

        new_books = []
        batch_size = settings.SCANNER_REDIS_CHECK_BATCH_SIZE

        # Обрабатываем пакетами
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i : i + batch_size]
            batch_new_books = self._process_deduplication_batch(batch)
            new_books.extend(batch_new_books)

            # Логируем прогресс для больших объемов
            if len(candidates) > 10000 and (i + batch_size) % 10000 == 0:
                logger.info(f"📊 Обработано {i + batch_size}/{len(candidates)} кандидатов")

        logger.info(f"✅ Дедупликация завершена. Новых книг: {len(new_books)}")
        return new_books

    def _process_deduplication_batch(self, batch: list[BookCandidate]) -> list[BookCandidate]:
        """Обрабатывает один пакет кандидатов через дедупликацию.

        Args:
            batch: Пакет кандидатов

        Returns:
            list[BookCandidate]: Новые книги из этого пакета
        """
        # Шаг 1: Пакетная проверка Redis SET (активные задачи)
        candidates_after_redis = self._batch_check_redis_queues(batch)

        if not candidates_after_redis:
            return []

        # Шаг 2: Пакетная проверка карантина PostgreSQL (новая логика)
        candidates_after_quarantine = self._batch_check_quarantine(candidates_after_redis)

        if not candidates_after_quarantine:
            return []

        # Шаг 3: Пакетная проверка PostgreSQL (обработанные книги)
        new_books = self._batch_check_postgresql(candidates_after_quarantine)

        return new_books

    def _batch_check_redis_queues(self, candidates: list[BookCandidate]) -> list[BookCandidate]:
        """Пакетная проверка активных задач в Redis SET.

        Args:
            candidates: Кандидаты для проверки

        Returns:
            list[BookCandidate]: Кандидаты, которых НЕТ в активных очередях
        """
        try:
            # Формируем ключи для Redis SISMEMBER
            keys = [candidate.get_key() for candidate in candidates]

            # Выполняем пакетную проверку через pipeline
            pipe = self.redis_client.pipeline()
            for key in keys:
                pipe.sismember(settings.SET_QUEUED_IDS, key)

            results = pipe.execute()

            # Фильтруем кандидатов
            candidates_not_in_queue = []
            for candidate, is_in_queue in zip(candidates, results):
                if is_in_queue:
                    self.stats.already_in_queue += 1
                    logger.debug(f"⏭️ Уже в очереди: {candidate.get_key()}")
                else:
                    candidates_not_in_queue.append(candidate)

            return candidates_not_in_queue

        except Exception as e:
            logger.error(f"❌ Ошибка проверки Redis очередей: {e}")
            raise

    def _batch_check_postgresql(self, candidates: list[BookCandidate]) -> list[BookCandidate]:
        """Пакетная проверка обработанных книг в PostgreSQL.

        Args:
            candidates: Кандидаты для проверки

        Returns:
            list[BookCandidate]: Кандидаты, которых НЕТ в book_sources
        """
        try:
            # Группируем IDs по source_type для эффективного запроса (избегаем composite types)
            ids_per_type: dict[int, list[int]] = {}
            for c in candidates:
                ids_per_type.setdefault(c.source_type, []).append(c.source_id)

            processed_pairs: set[tuple[int, int]] = set()

            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    for source_type, id_list in ids_per_type.items():
                        # Убираем дубли чтобы снизить нагрузку на IN
                        unique_ids = list(set(id_list))
                        # Пропускаем пустые списки (теоретически не нужны)
                        if not unique_ids:
                            continue

                        # Используем массив INT[] и ANY для быстрой проверки
                        cur.execute(
                            """
                            SELECT source_id
                            FROM book_sources
                            WHERE source_type = %s AND source_id = ANY(%s)
                            """,
                            (source_type, unique_ids),
                        )

                        for row in cur.fetchall():
                            processed_pairs.add((source_type, row[0]))

            # Фильтруем кандидатов
            new_candidates = []
            for candidate in candidates:
                pair = (candidate.source_type, candidate.source_id)
                if pair in processed_pairs:
                    self.stats.already_processed += 1
                    logger.debug(f"⏭️ Уже обработано: {candidate.get_key()}")
                else:
                    new_candidates.append(candidate)

            return new_candidates

        except Exception as e:
            logger.error(f"❌ Ошибка проверки PostgreSQL: {e}")
            raise

    def _batch_check_quarantine(self, candidates: list[BookCandidate]) -> list[BookCandidate]:
        """Пакетная проверка карантина в PostgreSQL.

        Новая логика: проверяем таблицу quarantined_books для фильтрации
        уже помещенных в карантин книг.

        Args:
            candidates: Кандидаты для проверки

        Returns:
            list[BookCandidate]: Кандидаты, не находящиеся в карантине
        """
        if not candidates:
            return []

        try:
            # Формируем список пар (source_type, source_id) для пакетной проверки
            candidate_pairs = [(candidate.source_type, candidate.source_id) for candidate in candidates]

            # Пакетная проверка карантина
            quarantined_pairs = get_quarantined_ids(candidate_pairs)

            # Фильтруем кандидатов
            new_candidates = []
            for candidate in candidates:
                pair = (candidate.source_type, candidate.source_id)
                if pair in quarantined_pairs:
                    self.stats.already_in_quarantine += 1
                    logger.debug(f"🚫 В карантине: {candidate.get_key()}")
                else:
                    new_candidates.append(candidate)

            return new_candidates

        except Exception as e:
            logger.error(f"❌ Ошибка проверки карантина: {e}")
            raise

    def _atomic_queue_tasks(self, new_books: list[BookCandidate]) -> None:
        """Атомарно добавляет новые задачи в очередь.

        Использует Redis pipeline для атомарности операций:
        - LPUSH в QUEUE_PARSING_NEW
        - SADD в SET_QUEUED_IDS

        Args:
            new_books: Список новых книг для постановки в очередь
        """
        if not new_books:
            return

        logger.info(f"📤 Добавляем {len(new_books)} новых задач в очередь")

        try:
            pipe = self.redis_client.pipeline()

            for book in new_books:
                # Формируем JSON задачи в новом формате (по плану)
                task_data = {
                    "source_type": book.source_type,
                    "source_id": book.source_id,
                    "archive_path": book.archive_path,
                    "book_filename": book.book_filename,
                    "archive_mtime": book.archive_mtime,
                }

                task_json = json.dumps(task_data)

                # Добавляем две операции в пайплайн
                pipe.lpush(settings.QUEUE_PARSING_NEW, task_json)
                pipe.sadd(settings.SET_QUEUED_IDS, book.get_key())

            # Выполняем все операции атомарно
            pipe.execute()

            self.stats.new_tasks_created = len(new_books)
            logger.info(f"✅ Успешно добавлено {len(new_books)} задач в очередь")

        except Exception as e:
            logger.error(f"❌ Ошибка добавления задач в очередь: {e}")
            raise
